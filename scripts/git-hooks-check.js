#!/usr/bin/env node

/**
 * Git Hooks 检查脚本
 * 用于在 pre-commit 和 pre-push 阶段执行各种代码质量检查
 */

const { execSync } = require('child_process');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description, options = {}) {
  const { optional = false, showOutput = false } = options;
  
  try {
    log(`🔍 ${description}...`, 'blue');
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: showOutput ? 'inherit' : 'pipe',
      cwd: process.cwd()
    });
    
    if (!showOutput && output.trim()) {
      console.log(output);
    }
    
    log(`✅ ${description} 通过`, 'green');
    return true;
  } catch (error) {
    if (optional) {
      log(`⚠️  ${description} 失败（可选检查）: ${error.message}`, 'yellow');
      return false;
    } else {
      log(`❌ ${description} 失败: ${error.message}`, 'red');
      throw error;
    }
  }
}

function checkPreCommit() {
  log('🚀 开始 pre-commit 检查...', 'cyan');
  
  // 1. 运行 lint-staged
  execCommand('pnpm run precommit', '代码格式化和 ESLint 检查');
  
  // 2. TypeScript 类型检查
  execCommand('pnpm run tsc', 'TypeScript 类型检查');
  
  // 3. 检查 console.log（可选）
  try {
    const files = execSync('git diff --cached --name-only', { encoding: 'utf8' })
      .split('\n')
      .filter(file => file.match(/\.(js|jsx|ts|tsx|vue)$/));
    
    if (files.length > 0) {
      for (const file of files) {
        try {
          const hasConsoleLog = execSync(`grep -l "console\\.log" "${file}"`, { encoding: 'utf8' });
          if (hasConsoleLog.trim()) {
            log(`⚠️  警告: 发现 console.log 在文件: ${file}`, 'yellow');
          }
        } catch (e) {
          // 文件中没有 console.log，这是正常的
        }
      }
    }
  } catch (e) {
    // 没有暂存的文件或其他错误，忽略
  }
  
  // 4. 检查当前分支
  try {
    const branch = execSync('git symbolic-ref --short HEAD', { encoding: 'utf8' }).trim();
    if (branch === 'main' || branch === 'master') {
      log(`❌ 错误: 不允许直接提交到 ${branch} 分支，请使用功能分支`, 'red');
      process.exit(1);
    }
  } catch (e) {
    // 可能在 detached HEAD 状态，允许继续
  }
  
  log('✅ pre-commit 检查全部通过！', 'green');
}

function checkPrePush() {
  log('🚀 开始 pre-push 检查...', 'cyan');
  
  // 1. 同步远程分支信息
  execCommand('pnpm run prepush', '同步远程分支信息');
  
  // 2. TypeScript 构建检查
  execCommand('pnpm run tsc:build', 'TypeScript 构建检查');
  
  // 3. TypeScript 类型检查
  execCommand('pnpm run tsc', 'TypeScript 类型检查');
  
  // 4. 运行测试
  execCommand('pnpm run test:ci', '运行测试');
  
  // 5. ESLint 检查
  execCommand('pnpm run lint', 'ESLint 检查');
  
  // 6. 可选：检查过时依赖
  execCommand('pnpm outdated --depth=0', '检查过时依赖', { optional: true });
  
  log('✅ pre-push 检查全部通过！', 'green');
}

// 主函数
function main() {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'pre-commit':
        checkPreCommit();
        break;
      case 'pre-push':
        checkPrePush();
        break;
      default:
        log('用法: node scripts/git-hooks-check.js [pre-commit|pre-push]', 'yellow');
        log('或者直接运行对应的 Git 钩子', 'yellow');
        process.exit(1);
    }
  } catch (error) {
    log(`检查失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkPreCommit, checkPrePush };
