#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo "🚀 开始 pre-push 检查..."

# 1. 获取最新的远程分支信息
echo "📡 正在同步远程分支信息..."
pnpm run prepush

# 2. 运行 TypeScript 构建检查（完整构建）
echo "🔨 正在运行 TypeScript 构建检查（这可能需要一些时间）..."
pnpm run tsc:build || {
  echo "❌ TypeScript 构建失败，推送终止。请修复所有构建错误后再尝试推送。"
  echo "💡 提示: 运行 'pnpm run tsc:build' 查看详细错误信息"
  exit 1
}

# 3. 运行类型检查（确保没有类型错误）
echo "🔍 正在运行 TypeScript 类型检查..."
pnpm run tsc || {
  echo "❌ TypeScript 类型检查失败，推送终止。请修复所有类型错误后再尝试推送。"
  echo "💡 提示: 运行 'pnpm run tsc' 查看详细错误信息"
  exit 1
}

# 4. 运行测试
echo "🧪 正在运行测试..."
pnpm run test:ci || {
  echo "❌ 测试失败，推送终止。请修复所有测试用例后再尝试推送。"
  echo "💡 提示: 运行 'pnpm run test:ci' 查看详细错误信息"
  exit 1
}

# 5. 运行 ESLint 检查
# echo "📝 正在运行 ESLint 检查..."
# pnpm run lint || {
#   echo "❌ ESLint 检查失败，推送终止。请修复所有代码规范问题后再尝试推送。"
#   echo "💡 提示: 运行 'pnpm run lint' 查看详细错误信息"
#   exit 1
# }

echo "✅ 所有检查通过，正在推送..."

# 可选：检查过时的依赖（如果太慢可以注释掉）
# echo "📦 检查过时的依赖..."
# pnpm outdated --depth=0
