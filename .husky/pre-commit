#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo "🚀 开始 pre-commit 检查..."

# 1. 运行 lint-staged 进行代码格式化和 ESLint 检查
# echo "📝 运行代码格式化和 ESLint 检查..."
# pnpm run precommit || { echo "❌ 代码格式化或 ESLint 检查失败"; exit 1; }

# 2. 运行 TypeScript 类型检查（仅检查，不构建）
echo "🔍 运行 TypeScript 类型检查..."
pnpm run tsc:build || { echo "❌ TypeScript 类型检查失败，请修复类型错误后再提交"; exit 1; }

# 3. 检查是否有 console.log 语句（警告，不阻止提交）
echo "🔍 检查 console.log 语句..."
git diff --cached --name-only | grep -E '\.(js|jsx|ts|tsx|vue)$' | xargs grep -l 'console\.log' | while read file; do
  echo "⚠️  警告: 发现 console.log 在文件: $file"
done

# 4. 防止直接提交到 main/master 分支
branch="$(git symbolic-ref HEAD 2>/dev/null)" || branch="(unnamed branch)"
branch=${branch##refs/heads/}
if [ "$branch" = "main" ] || [ "$branch" = "master" ]; then
  echo "❌ 错误: 不允许直接提交到 $branch 分支，请使用功能分支"
  exit 1
fi

# 5. 检查大文件（超过 500KB）
echo "📦 检查大文件..."
git diff --cached --name-only | while read file; do
  if [ -f "$file" ]; then
    size=$(du -k "$file" | cut -f1)
    if [ $size -gt 500 ]; then
      echo "❌ 错误: $file 文件大小超过 500KB，请重新考虑是否需要提交此文件"
      exit 1
    fi
  fi
done

echo "✅ pre-commit 检查通过！"
