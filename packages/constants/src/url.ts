/**
 * 运行环境
 * @constant 本地 ｜ 预发 ｜ 线上
 */
export type IDeployEnv = 'dev' | 'rd' | 'qa' | 'pre' | 'prod';

// 获取部署环境的函数
export function getDeployEnv(): IDeployEnv {
  // 在packages中，我们不能直接访问import.meta.env
  // 这个值应该由应用层传入或通过其他方式获取
  return 'dev';
}

export function getUrls(deployEnv: IDeployEnv) {
  const urls = {
    rtcUrl: '',
    wsUrl: '',
  };

  switch (deployEnv) {
    case 'pre':
      urls.rtcUrl = 'https://pre-nebula.zhipin.com';
      urls.wsUrl = 'wss://pre-kaoshi.zhipin.com/kanjian/ws';
      break;
    case 'prod':
      urls.rtcUrl = 'https://nebula.zhipin.com';
      urls.wsUrl = 'wss://kaoshi.zhipin.com/kanjian/ws';
      break;
    default:
      urls.rtcUrl = 'https://qa-nebula.weizhipin.com';
      urls.wsUrl = 'wss://kaoshi-qa.weizhipin.com/kanjian/ws';
      break;
  }

  return urls;
}
