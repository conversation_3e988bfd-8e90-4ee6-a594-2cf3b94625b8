import { EDITOR_LANGUAGES, EXECUTE_STATUS, LANGUAGE_STATUS } from '../components/monaco-editor/constants';

export const previewData = {
    // 点击执行按钮
    executeProgramPreview(incoming: any) {
        incoming.executeStatus.value = EXECUTE_STATUS.运行中;
        incoming.output.value = '';
        incoming.selfTestAfterExecute.value = '';
        setTimeout(() => {
            incoming.executeStatus.value = EXECUTE_STATUS.已完成;
            incoming.output.value = '模拟运行结果';
            incoming.selfTestAfterExecute.value = incoming.selfTest.value;
        }, 1000);
    },
    // 获取已选语言
    getLanguagePreview(incoming: any) {
        incoming.currentLanguage.value = EDITOR_LANGUAGES[0]?.editorLanguage;
        incoming.editorOptions.value.language = incoming.currentLanguage.value;
        incoming.languageStatus.value = LANGUAGE_STATUS.已选;
    },
};
