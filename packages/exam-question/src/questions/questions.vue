<template>
    <div v-if="dimensionList.length > 0" class="question-list">
        <div v-for="dimensionItem in dimensionList" :key="dimensionItem.encryptDimensionId || ''">
            <DimensionTitle v-if="dimensionItem.questionList && dimensionItem.questionList.length > 0" :dimensionItem="dimensionItem" />
            <div v-for="(questionItem, questionIndex) in dimensionItem.questionList" :key="questionItem.encryptQuestionId || ''">
                <SingleQuestion
                    v-if="dimensionItem.questionList![questionIndex]"
                    v-model="dimensionItem.questionList![questionIndex]"
                    :questionIndex
                    v-bind="dimensionItem.scoreExamEnable !== undefined ? { scoreExamEnable: dimensionItem.scoreExamEnable } : {}"
                    @answerSave="answerSave"
                />
            </div>
        </div>
    </div>
    <PreviewImage ref="previewImageRef" />
</template>

<script setup lang="ts">
import type { DimensionData } from './types';
import { PreviewImage } from '@crm/exam-components';
import { provide, ref } from 'vue';
import DimensionTitle from './dimension-title.vue';
import SingleQuestion from './single-question.vue';

defineOptions({
    name: 'Questions',
});
const dimensionList = defineModel<DimensionData[]>({
    required: true,
});
const emit = defineEmits<{
    answerSave: [params: any];
}>();
function answerSave(params: any) {
    emit('answerSave', params);
}

const previewImageRef = ref();
// 预览图片
provide('preview-image', {
    open: (src: string) => {
        previewImageRef.value.open(src);
    },
});
</script>

<style lang="less" scoped>
.question-list {
    // width: 600px;

    .dimension-title {
        font-size: 18px;
        margin-bottom: 24px;
        display: flex;

        .title-text {
            color: #2d2d2d;
            font-weight: 500;
        }
        .title-score {
            white-space: nowrap;
            color: #9fa6b5;
        }
    }

    :deep(.question-item) {
        margin-bottom: 24px;

        .title {
            margin-bottom: 10px;

            .score {
                color: #9fa6b5;
            }
        }

        .title-number {
            margin-right: 5px;
            font-weight: 500;
        }
        .title-content {
            font-weight: 500;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .img-list {
            display: flex;
            flex-wrap: wrap;
            margin-left: 18px;
            &.img-list-child {
                margin-left: 36px;
            }
            .attachment-wrap {
                margin-bottom: 10px;
                img {
                    display: block;
                    width: 120px;
                    height: 120px;
                    margin-right: 8px;
                }
            }
        }
        .child-item {
            & + .child-item {
                margin-top: 10px;
            }

            .child-title {
                margin-bottom: 8px;
            }
        }

        // .choices-wrap {
        //     margin-left: 18px;
        // }

        .choice-item {
            .ui-radio-label {
                padding-left: 6px;
            }

            // & + .choice-item {
            //     margin-top: 10px;
            // }
        }

        .b-textarea {
            min-height: 100px;
        }
    }
}
</style>
