<template>
    <div v-if="debuggingStore.hasInitStep" class="page-wrap-monitor">
        <router-view />
        <!-- mqtt沟通入口 -->
        <ChatEntry v-if="hasChat" :examId="examId" :seqId="seqId" @forceCommitPaper="forceCommitPaper" />
    </div>
    <b-pagetip v-else :status="pageStatus" loadingType="dot">
        <template #error-content>
            <b-space direction="vertical">
                <p class="b-pagetip-text">数据加载失败，请点击按钮重试！</p>
                <b-button type="primary" style="margin: 0 auto" @click.prevent="retry"> 重试 </b-button>
            </b-space>
        </template>
    </b-pagetip>
</template>

<script lang="ts" setup>
import { useIsMobile } from '@crm/exam-hooks';
import { ExamStatusEnum } from '@crm/exam-types';
import { useDebuggingStore } from './store';
import { useMonitorStore } from '@/store/use-monitor-store';
import { MonitorStage } from './config';
import { commitPaper } from './utils';
import { useRouteParamId } from './hooks/useRouteParamId';
import ChatEntry from './components/chat-entry.vue';

/**
 * 请求状态枚举
 * 用于跟踪考试信息请求的状态
 */
enum RequestStatus {
    INITIAL = '初始化, 请求中', // 初始化状态
    SUCCESS = '请求成功', // 请求成功
    FAILURE = '请求失败', // 请求失败
}

const { isMobileView } = useIsMobile();
const debuggingStore = useDebuggingStore();
const monitorStore = useMonitorStore();
const $route = useRoute();
const $router = useRouter();
const { seqId, examId } = useRouteParamId();

const hasChat = computed(() => {
    const { hasInvigilator } = monitorStore.examBaseInfo.examInfo || {};
    const isExamMode = monitorStore.ExamMode === 'EXAM_MODE'; // 考试阶段 与 考前设备阶段区分
    const chatEnabled = hasInvigilator && debuggingStore.examConfig.openCommunicateInlet && isExamMode && !isMobileView.value;
    return chatEnabled;
});

const forceCommitPaper = async () => {
    await commitPaper({
        type: 'force',
        examId,
        seqId,
    });
};

// 考试信息请求状态
const examInfoRequestStatus = ref<RequestStatus>(RequestStatus.INITIAL);
const isLoading = ref(true);
const pageStatus = computed(() => {
    let status = 'empty' as 'empty' | 'loading' | 'error';
    if (!debuggingStore.hasInitStep || isLoading.value) {
        status = 'loading';
    } else if (examInfoRequestStatus.value === RequestStatus.FAILURE) {
        status = 'error';
    }

    return status;
});

const getExamConfig = async () => {
    isLoading.value = true;
    const { code } = await debuggingStore.fetchConfig({ seqId, examId });

    try {
        if (code === 0) {
            await debuggingStore.fetchStep({ seqId, examId });
        } else {
            examInfoRequestStatus.value = RequestStatus.FAILURE;
        }
    } catch (error) {
        examInfoRequestStatus.value = RequestStatus.FAILURE;
    } finally {
        isLoading.value = false;
    }
};

function retry() {
    window.location.reload();
}

const stageComponents = {
    [MonitorStage.Answering]: '/monitor/answering', // 正式考试答题阶段
    [MonitorStage.PrepareDone]: '/monitor/ready/done', // 考前准备阶段完成
};

watch(
    () => [debuggingStore.hasInitStep, debuggingStore.step] as const,
    ([isLoading, step]) => {
        if (isLoading) {
            let url = '/monitor/ready';
            if (stageComponents[step as keyof typeof stageComponents]) {
                url = stageComponents[step as keyof typeof stageComponents];
            }

            $router.replace({
                path: url,
                query: $route.query,
            });
        }
    },
    { immediate: true },
);

onMounted(async () => {
    debuggingStore.resetData();
    if (isMobileView.value) {
        $router.replace({
            path: `/status/${seqId}`,
            query: {
                status: ExamStatusEnum.兼容拦截,
                text: '请用电脑端登录考试',
            },
        });

        return;
    }
    watch(
        () => [!debuggingStore.hasInitStep, monitorStore.examBaseInfo.examInfo?.examId, monitorStore.debugInfo?.canDebug],
        async ([isLoad, examId, canDebug]) => {
            // 小场次考试信息加载完成
            if ((isLoad && examId) || canDebug) {
                await getExamConfig();
                await debuggingStore.initBodyDetect();
            }
        },
        {
            immediate: true,
        },
    );
});

onBeforeUnmount(() => {
    debuggingStore.closeRtcAll(examId);
    debuggingStore.resetData();
});
</script>

<style lang="less">
@import './index.less';
</style>
