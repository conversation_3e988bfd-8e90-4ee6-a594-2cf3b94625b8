<template>
    <div :id="`dom${domId}`" class="result-chart">
        <div v-for="(_number, index) in 10" :key="index" class="scale-item">
            <div class="tr-line">
                <p v-if="configList[index] || configList[index] === 0" :style="{ height: `${100 - configList[index]}%` }" />
                <p v-if="configList[index] || configList[index] === 0" class="boll" />
            </div>
            <em class="scale-line" />
        </div>
        <svg class="svg-wrap">
            <!-- <animate attributeName="points" dur="3s" repeatCount="indefinite" :values="animateValues"> -->
            <polyline :points="points" style="stroke: var(--primary-color); stroke-width: 1; fill: none" />
            <!-- </animate> -->
        </svg>
        <!-- <p>{{ animateValues }}</p> -->
    </div>
</template>

<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';

const props = defineProps({
    domId: {
        type: Number,
        default: () => 0,
    },
    chartList: {
        type: Array,
        default: () => [],
    },
    max: {
        type: Number,
        default: () => 0,
    },
});

const configList = ref<number[]>([]);
const positionList = ref<any[]>([]);
const points = ref('');
const animateValues = ref('');

function percentage(list: number[]) {
    const max = Math.max(Math.max(...list, props.max));
    const valList = list.map((number: number) => {
        const n = Math.ceil(Number((number / max).toFixed(2)) * 100);

        return n || 0;
    });
    configList.value = valList;

    // setTimeout(() => {
    nextTick(() => {
        svgInit();
    });
    // }, 1000);
}

function svgInit() {
    const ballDomList = document.querySelectorAll(`#dom${props.domId} .tr-line .boll`);
    const offsetList: any[] = [];
    let str = '';
    ballDomList.forEach((dom, index) => {
        const element = dom as HTMLElement;
        const top = element.offsetTop + element.clientHeight / 2;
        const left = element.offsetLeft + element.clientWidth / 2 - 0.5;

        str += `${left} ${top}${index < ballDomList.length - 1 ? ',' : ''}`;

        offsetList.push({
            top,
            left,
        });
    });

    animateValues.value += `${str};`;

    points.value = str;
    positionList.value = [...offsetList];
}
watch(
    () => props.chartList,
    (val) => {
        percentage([...val] as number[]);
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.result-chart {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 5px 5px 0;
    display: flex;
    justify-content: space-between;
    background: #ffffff;
    box-shadow:
        inset 3px 3px 4px 0px rgba(6, 8, 9, 0.08),
        inset -2px -2px 3px 0px rgba(255, 255, 255, 0.9),
        4px 4px 6px 0px rgba(0, 0, 0, 0.04);
    border: 1px solid #ececec;
    border-radius: 6px;

    .svg-wrap {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }

    .scale-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        height: 100%;

        .tr-line {
            flex: 1;
            display: flex;
            flex-direction: column;
            width: 6px;

            .boll {
                width: 6px;
                min-width: 6px;
                height: 6px;
                min-height: 6px;
                border-radius: 5px;
                background: var(--primary-color);
                //
                position: relative;
                z-index: 2;

                &::after {
                    content: '';
                    position: absolute;
                    top: 1px;
                    left: 1px;
                    right: 1px;
                    bottom: 1px;
                    background: #fff;
                    border-radius: 50%;
                }
            }
        }

        .scale-line {
            margin-top: 5px;
            // margin-left: 2px;
            width: 1px;
            height: 4px;
            // border-left: 1px solid ;
            background: #dfdfdf;
        }
    }
}
</style>
