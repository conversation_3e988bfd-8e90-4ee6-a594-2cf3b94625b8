<template>
    <AnswerDialog :isPreview="isPreview" />
    <slot />
    <RoundLines v-if="showRoundLinesComponents" ref="connectLinesRef" @onAnswerChange="setAnswerChange" />
    <DoubleRowLines v-else ref="connectLinesRef" @onAnswerChange="setAnswerChange" />
</template>

<script setup lang="ts" name="ConnectLines">
import AnswerDialog from '../answer-dialog/index.vue';
import { useHotsStore } from '../../store';
import { computed, ref } from 'vue';
import DoubleRowLines from './double-row-lines.vue';
import RoundLines from './round-lines.vue';

defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    },
    isPreview: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue', 'onAnswerChange']);
const paperStore = useHotsStore();

const showRoundLinesComponents = computed(() => {
    const { outputParamCount, outputParamList } = paperStore?.currentQuestion?.product10QuestionParamVO || {};
    return !outputParamCount && !outputParamList?.length;
});
const connectLinesRef = ref();

function getAnswerData() {
    return connectLinesRef.value.getAnswerData();
}

function setAnswerChange() {
    emit('onAnswerChange', getAnswerData());
}

defineExpose({
    getAnswerData,
    validate: () => paperStore.answerDialogController.validate(),
});
</script>
