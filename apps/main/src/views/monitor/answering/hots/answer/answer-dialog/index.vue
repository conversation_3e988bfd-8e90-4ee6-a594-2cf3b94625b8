<template>
    <div v-if="answerDialogController.clickPopQueue.length > 0" :class="classes.tip" @click="() => answerDialogController.openDialog('click-pop')">
        <div>
            <div>判断题</div>
        </div>
        <div :class="classes['tip-opt']">
            <div>待完成</div>
            <div :class="classes['tip-opt-round']">
                {{ answerDialogController.clickPopQueue.length }}
            </div>
            <div>
                <SvgIcon style="margin-bottom: 2px" width="16" height="16" name="up-arrow" />
            </div>
        </div>
    </div>
    <b-dialog
        v-if="dialogOpened"
        v-model="dialogOpened"
        :enableEscClose="false"
        :layerClosable="false"
        :showClose="false"
        showInPopupContainer
        :width="426"
        popupContainer=".answer-wrap"
        :footerClass="classes['custom-dialog-footer']"
        :modalClass="classes['custom-dialog-model']"
        :bodyClass="classes['custom-dialog-body']"
    >
        <template #title>
            <div style="display: flex; gap: 6px; align-items: center">
                <div style="font-size: 18px">
                    请判断以下信息
                    <template v-if="popQueue.length > 1">
                        {{ `· ${popQueue.length}` }}
                    </template>
                </div>
            </div>
            <div :class="classes['close-dialog']" @click="answerDialogController.closeDialog"><SvgIcon style="margin-right: 4px" name="close-side" />暂不回答</div>
        </template>
        <div style="display: flex; flex-direction: column; gap: 24px">
            <template v-for="(queue, index) in popQueue" :key="index">
                <div style="display: flex; flex-direction: column; gap: 16px">
                    <div :class="classes['question-title']">
                        {{ queue?.question.questionSnapshot.questionTitle }}
                    </div>
                    <div v-if="queue.question.questionSnapshot.questionOptionList" :class="classes.opt">
                        <template v-for="item in queue.question.questionSnapshot.questionOptionList" :key="item">
                            <b-button
                                :class="{ [classes['opt-buttton']]: true, [classes['opt-buttton-mobile']]: isMob }"
                                type="outline"
                                @click="handleActionClick(queue.question, item.encryptId)"
                            >
                                {{ item.optionContent }}
                            </b-button>
                        </template>
                    </div>
                </div>
                <div :class="popQueue.length > index + 1 && classes['divide-line']" />
            </template>
        </div>
    </b-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useHotsStore } from '../../store';
// import { isMobile } from '@/utils'
import { useIsMobile } from '@crm/exam-hooks';

defineOptions({
    name: 'AnswerDialog',
});

const props = defineProps({
    isPreview: {
        type: Boolean,
        default: false,
    },
});

const isMob = computed(() => useIsMobile());

const { answerDialogController, postQuestion } = useHotsStore();
const dialogOpened = computed(() => answerDialogController.dialogOpened);
const popQueue = computed(() => (answerDialogController.dialogType === 'auto-pop' ? answerDialogController.autoPopQueue : answerDialogController.clickPopQueue));

function handleActionClick(question: any, answerContent: string) {
    if (!props.isPreview) {
        postQuestion({
            evaPaperSnapshotId: question.encryptId,
            answerContent,
            questionType: question.questionSnapshot.questionType,
        });
    }

    answerDialogController.next(question);
}
</script>

<style module="classes">
.custom-dialog-model {
    border-radius: 16px !important;
    /* margin: 20px 0 !important; */
    max-height: min(calc(100% - 40px), 500px) !important;
}

.custom-dialog-body::-webkit-scrollbar-track-piece {
    display: none !important;
}

.custom-dialog-body::-webkit-scrollbar {
    width: 8px;
}

.tip {
    display: flex;
    justify-content: space-between;
    background: #d6ebff;
    border-radius: 8px;
    height: 36px;
    padding: 6px 12px;
    font-size: 14px;
    align-items: center;
    margin-bottom: 12px;
    cursor: pointer;
}

.tip-opt {
    display: flex;
    gap: 4px;
    font-size: 12px;
    color: #1f6ae0;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    align-items: center;
}

.tip-opt-round {
    background: #1f6ae0;
    border-radius: 10px;
    width: 22px;
    color: #fdfdfe;
    text-align: center;
}

.opt {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.opt-buttton {
    border-radius: 60px;
    width: 96px;
    height: 36px;
    font-size: 14px;
    color: #1f1f1f;
    border: 1px solid #dfdfdf !important;

    &:hover {
        background: #caebeb !important;
        border: 1px solid var(--primary-color-6) !important;
    }
}

.close-dialog {
    position: absolute;
    color: var(--点击, #0f9490);
    right: 20px;
    top: 22px;
    cursor: pointer;
    font-size: 12px;
    z-index: 998;
}

.custom-dialog-footer {
    display: none;
}

.opt-buttton-mobile {
    &:hover {
        background: none !important;
        border: 1px solid #dfdfdf !important;
    }
}
.question-title {
    color: var(--标题正文, #1f1f1f);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}

.big-number {
    color: #1f1f1f;
    font-family: 'Kanzhun';
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}
.small-number {
    color: #b3b3b3;
    font-family: 'Kanzhun';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.divide-line {
    width: 100%;
    height: 1px;
    min-height: 1px;
    background-color: #ececec;
}
</style>
