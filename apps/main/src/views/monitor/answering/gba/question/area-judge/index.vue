<template>
    <div class="round-content">
        <div class="title-wrap">
            <Text font-weight="600" stroke-width="0" :text="`${numberToChinese(gbaStore.currentIndex + 1, false)}、${gbaStore.currentQuestion.showName}`" />
            <div class="progress-wrap">
                <p :style="{ width: `${(roundPassed / roundList.length) * 100}%` }" class="bar" />
                <p class="text">剩余回合 {{ roundList.length - roundPassed }} 轮</p>
            </div>
            <TotalFund :goldNum="goldNum" :reservedDecimalPlaces="1" :strokeWidth="0" />
            <FadeInText class="gold-num-fade" :fundAnimationConfig="fundAnimationConfig" />
        </div>
        <div class="game-box-wrap">
            <div
                v-for="item in areaConfigList"
                :key="`${item.areaId}-${gbaStore.currentIndex}`"
                class="question-item"
                :class="{
                    success: result && currentRoundSnapshot.appearAreaId === item.areaId,
                    fail: result === 0 && currentRoundSnapshot.appearAreaId === item.areaId,
                    'transition-state': transitionState,
                    'result-state': resultState,
                    timeout: timeoutState,
                    'no-title': !currentRoundSnapshot.showAreaQuestion,
                }"
            >
                <div v-if="currentRoundSnapshot.showAreaQuestion" class="title">
                    <template v-if="currentRoundSnapshot.appearAreaId === item.areaId">
                        {{ item.title }}
                    </template>
                </div>
                <img v-if="currentRoundSnapshot.appearAreaId === item.areaId && item.imgUrl" height="90" width="90" :src="item.imgUrl" />
            </div>
        </div>
        <div v-if="showBtnGroup" class="option-button-wrap">
            <template v-for="item in optionList" :key="item.id">
                <div :class="{ err: item.status }" class="button-inner">
                    <p class="key-down-desc">快捷键“{{ item.shortcutKeys }}”</p>
                    <Button :disabled="btnGroupDisabled" :isActiveEffect="!btnGroupDisabled" :theme="item.theme" @click="handleSelect(item)">
                        <Text
                            font-weight="600"
                            :shadowFilterConfig="item.shadowFilterConfig"
                            stroke-width="1"
                            :strokeColor="item.strokeColor"
                            :text="item.content"
                            :textColor="item.color"
                            font-size="20px"
                        />
                    </Button>
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts" setup name="AreaJudge">
import { createTimer, timeCenter } from '@/store/time';
import { useNotification } from '../../component/notification-tips/index';
import { useGbaStore } from '../../store';
import { onKeyStroke } from '@crm/vueuse-pro';
import { sum } from 'lodash-es';
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import Button from '../../component/button.vue';
import FadeInText from '../../component/fadein-text.vue';
import Text from '../../component/text.vue';
import TotalFund from '../../component/total-fund.vue';
import { delay, OPTION_LIST_MAP } from './const';
import { numberToChinese } from '@/utils/number-to-chinese';

const props = defineProps({
    isSimulate: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(['roundOver', 'gameOver', 'roundStart', 'roundChange']);
const gbaStore = useGbaStore();
const { currentTime } = timeCenter;
const useNotificationTips = useNotification();

const fundAnimationConfig = ref({
    show: false,
    text: '+0',
    action: '+',
});

// 游戏回合数据
const roundList = computed(() => {
    if (props.isSimulate) {
        // 模拟模式下 使用模拟数据
        return gbaStore.currentQuestion.simulateList;
    } else {
        return gbaStore.currentQuestion.roundList;
    }
});
// 已处理回合数
const roundPassed = ref(0);
// 当前回合数据
const currentRoundData: any = computed(() => roundList.value[roundPassed.value]);
// 作答之后的回合数据
const answerList: any = ref([]);
// 最后一次作答并且不超时的数据
const lastRoundData: any = computed(() => {
    const roundData = answerList.value?.length ? answerList.value : roundList.value;
    const data: any = roundData.slice().reverse();
    const lastItem = data?.find((item: { answerTimeout?: number; answerStatus?: number }) => item.answerTimeout === 0 && item.answerStatus === 1);
    return lastItem || {};
});
// 当前回合游戏数据快照
const currentRoundSnapshot = computed(() => currentRoundData.value?.roundSnapshot || {});
// 操作按钮(是、否)
const optionList = computed(() =>
    currentRoundData.value?.optionList.map((item: any) => ({
        ...item,
        ...(OPTION_LIST_MAP[item.paramId] || {}),
    })),
);

// 缓存已选择的数据，用于数据提交与
let selectData = reactive(defaultSelectData());

// 题目图片配置
const assetsConfig: any = computed(() => {
    return gbaStore.currentQuestion.assetsCongig;
});

const defaultAreaConfigList = function () {
    return [
        {
            areaId: 0,
            title: '',
            imgUrl: '',
        },
        {
            areaId: 0,
            title: '',
            imgUrl: '',
        },
    ];
};

// 区域id和区域的题干映射
const areaConfigList = ref(defaultAreaConfigList());

function defaultSelectData() {
    return {
        encSnapshotId: '', // 当前回合id
        paramId: 0, // 用户选择的数据
        chooseTimeTs: 0, // 用户选择的时间点
        showTimeTs: 0, // 当前回合首次展示的时间
        timeoutTs: 0, // 当前回合超时时间点 (与 chooseTime 互斥);
        score: 0, // 获得分数
    };
}

// 是否展示参与or不参与按钮
const showBtnGroup = ref(true);
const btnGroupDisabled = ref(true);

const goldNum = ref<number>(0);
// 参与游戏结果(作答正确或者作答错误)
const result = ref();
// 游戏是否超时
const timeoutState = ref();
// 显示结果状态
const resultState = ref();
const roundCountdown = createTimer();
// 题目选择到显示结果过渡状态
const transitionState = ref();
const fundAnimationText = ref(0);
// 点击是否
async function handleSelect(row: { id: number; correct: number; paramId: number }) {
    if (btnGroupDisabled.value) {
        return;
    }
    selectData.chooseTimeTs = currentTime.value;
    selectData.paramId = row.paramId;

    btnGroupDisabled.value = true;

    result.value = row.correct;
    transitionState.value = true;

    // 停止倒计时
    roundCountdown.stop();
    if (row.correct === 1) {
        const { appearAreaId } = lastRoundData.value?.roundSnapshot || {};
        fundAnimationText.value = 1.5;
        if (currentRoundSnapshot.value.appearAreaId === appearAreaId) {
            fundAnimationText.value = 1;
        }
        fundAnimationConfig.value = {
            show: true,
            text: `+${fundAnimationText.value}`,
            action: '+',
        };
        await delay(0.3);

        goldNum.value = goldNum.value + fundAnimationText.value;
        transitionState.value = false;
        resultState.value = true;
        answerList.value.push({ ...currentRoundData.value, answerTimeout: 0, answerStatus: 1 });
    }
    roundEnd();
}

// 设置回合默认数据
function setRoundData(index: number = -1) {
    const roundPassedIndex = index > -1 ? index : roundPassed.value;
    const roundData: any = roundList.value[roundPassedIndex];

    const newAreaConfigList = gbaStore.currentQuestion.areaConfigList?.map((item) => ({
        areaId: Number(item.areaId),
        title: item.title,
        imgUrl: assetsConfig.value?.find((a: { paramId: any }) => a.paramId === currentRoundSnapshot.value.appearObjectId)?.paramImg || '',
    }));

    areaConfigList.value = newAreaConfigList || [];

    // 设置已选择的数据
    selectData = {
        ...defaultSelectData(),
        showTimeTs: currentTime.value, // 设置回合曝光时间
        encSnapshotId: roundData.encryptId, // 回合ID
        timeoutTs: 0,
        paramId: 0,
    };
}

function resetStatus() {
    areaConfigList.value = defaultAreaConfigList();
    btnGroupDisabled.value = false;
    transitionState.value = false;
    result.value = null;
    timeoutState.value = false;
    resultState.value = false;
    fundAnimationText.value = 0;
    fundAnimationConfig.value = {
        show: false,
        text: `+0`,
        action: '+',
    };
}

// 回合结束
async function roundEnd(isTimeOut: boolean = false) {
    if (!currentRoundSnapshot.value) {
        return;
    }
    const params: any = {
        ...selectData,
        score: fundAnimationText.value,
    };
    if (isTimeOut) {
        params.timeoutTs = currentTime.value;
        delete params.paramId;
    }
    emits('roundOver', params);
    await delay((gbaStore.currentQuestion.resultAppearTime || 0) / 1000);
    roundPassed.value += 1;
    nextTick(() => {
        roundContinue();
    });
}

// 继续下一个回合
function roundContinue() {
    resetStatus();
    // 回合结束-游戏结束
    if (roundPassed.value >= roundList.value.length) {
        gameOver();
        btnGroupDisabled.value = true;
        emits('roundChange', { type: 'end' });
        return;
    }
    emits('roundChange', { currentQuestionId: currentRoundData.value.encryptId });

    nextTick(() => {
        setRoundData();
        // 使用新的 start 方法启动计时器
        roundCountdown?.start({
            key: 'roundCountdown',
            finishTime: (t) => t + gbaStore.currentQuestion.chooseTime,
            onFinished: async () => {
                selectData.timeoutTs = currentTime.value;
                // 倒计时结束时，如果玩家尚未做出选择，则提示"作答超时"，进入到下一轮
                btnGroupDisabled.value = true;
                answerList.value.push({ ...currentRoundData.value, answerTimeout: 1, answerStatus: 1 });
                result.value = 0;
                timeoutState.value = true;
                useNotificationTips.open({
                    type: 'tips-message',
                    text: '作答超时',
                    onClose: async () => {
                        roundEnd(true);
                    },
                });
            },
        });
    });
}

// 所有回合结束 游戏结束
function gameOver() {
    if (!props.isSimulate) {
        useNotificationTips.open({
            type: 'template-message',
            action: 'area-judge-end',
            theme: 'green-deep',
            reservedDecimalPlaces: 1,
            number: goldNum.value ? goldNum.value?.toFixed(1) : goldNum.value,
            onNext: () => {
                emits('gameOver');
            },
        });
    } else {
        emits('gameOver');
    }
}

// 通过键盘点击1、2来选择是否参与
onKeyStroke(['1', '2'], (e) => {
    e.preventDefault();
    const data = optionList.value?.find((item: { shortcutKeys: number }) => item.shortcutKeys === Number(e.key));
    if (data) {
        handleSelect(data);
    }
});
onMounted(() => {
    // 查看是否还有未答回合
    const index = roundList.value.findIndex((item) => item.answerStatus === 0);
    // 计算已答回合金额
    const number = sum(roundList.value.map((item) => item.roundSnapshot?.reward || 0));
    goldNum.value = number || 0;
    // 如果所有回合回答完毕 直接展示结算弹窗
    if (index < 0) {
        roundPassed.value = roundList.value.length;
        setRoundData(roundList.value.length - 1);
        gameOver();
    } else {
        roundPassed.value = index;

        useNotificationTips.open({
            type: 'countdown-start-message',
            onClose: () => {
                setRoundData();
                roundContinue();
            },
        });
    }
});
</script>

<style lang="less" scoped>
@import 'index';
</style>
