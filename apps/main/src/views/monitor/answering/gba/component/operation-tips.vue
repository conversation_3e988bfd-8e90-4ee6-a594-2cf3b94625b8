<template>
    <div class="operation-tips">
        <p class="title">作答说明</p>
        <p class="time-info">
            <span>共{{ examInfo?.questionCount }}题</span>
            <em class="line" />
            <span>限时{{ examInfo?.adviseDurationStr }}</span>
        </p>
        <div class="content">
            <RichText :html="examInfo?.answerInstruction || ''" />
        </div>
        <div class="options-btn">
            <b-button class="next-btn" type="primary" shape="round" @click="changeStep"> 阅读完毕，开始答题 </b-button>
        </div>
    </div>
</template>

<script setup lang="ts" name="OperationTips">
import { computed } from 'vue';
import RichText from '@/components/rich-text/index.vue';
import { useMonitorStore } from '@/store/use-monitor-store';
const monitorStore = useMonitorStore();
const { examBaseInfo } = monitorStore;
const examInfo = computed(() => examBaseInfo.examInfo);
const emits = defineEmits(['onOver']);

function changeStep() {
    emits('onOver');
}
</script>

<style lang="less" scoped>
.operation-tips {
    padding: 32px 0;
    margin: 0 auto;
    width: 1068px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(160.13423deg, #f4f7f9 4%, #ffffff 48%);
    box-shadow:
        4px 4px 10px 0px rgba(34, 41, 43, 0.1),
        -5px -4px 14px 0px #ffffff;
    border-radius: 7px;

    .title {
        padding: 0 0 24px;
        color: #1f1f1f;
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px;
        text-align: center;
    }

    .time-info {
        padding: 0 32px;
        color: #1f1f1f;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        display: flex;
        align-items: center;

        .line {
            margin: 0 8px;
            width: 1px;
            height: 11px;
            background: #ececec;
        }
    }

    .content {
        padding: 0 32px;
        margin-top: 12px;
        flex: 1;
        overflow-y: scroll;
    }

    .options-btn {
        margin-top: 24px;
        display: flex;
        justify-content: center;

        .next-btn {
            padding: 8px 27px;

            & + .next-btn {
                margin-left: 16px;
            }
        }
    }
}
</style>
