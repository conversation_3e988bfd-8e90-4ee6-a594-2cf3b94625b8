<template>
    <div
        class="title-container"
        :data-name="changedNumber"
        :style="{
            '--color': textColor,
            '--stroke-width': `${strokeWidth}px`,
            '--stroke-color': strokeColor,
            '--font-family': fontFamily,
            '--font-size': fontSize,
            '--font-weight': fontWeight,
            '--shadow': shadowFilterConfig
                ? `drop-shadow(${shadowFilterConfig?.x}px ${shadowFilterConfig?.y}px ${shadowFilterConfig?.blur}px ${shadowFilterConfig?.color})`
                : 'unset',
        }"
    >
        <AnimationNumber
            v-if="needAnimation && typeof text === 'number' && typeof localText === 'number'"
            :value="localText"
            :reservedDecimalPlaces="reservedDecimalPlaces || 0"
            @changeNumber="changeNumber"
        />
        <span v-else style="white-space: nowrap">{{ localText }}</span>
    </div>
</template>

<script setup lang="ts" name="CText">
import { ref, watch } from 'vue';
import AnimationNumber from './animation-number.vue';

interface IProps {
    text?: string | number;
    textColor?: string;
    strokeWidth?: string | number;
    strokeColor?: string;
    fontFamily?: string;
    fontSize?: string;
    fontWeight?: string | number;
    shadowFilterConfig?:
        | false
        | {
              x: number;
              y: number;
              blur: number;
              color: string;
          };
    needAnimation?: boolean;
    reservedDecimalPlaces?: number;
}

const props = withDefaults(defineProps<IProps>(), {
    text: '测试文本',
    textColor: '#2D2D2D',
    strokeWidth: 1,
    strokeColor: '#fff',
    fontFamily: 'PingFang SC',
    fontSize: '24px',
    fontWeight: 400,
    shadowFilterConfig: false,
    needAnimation: false,
    reservedDecimalPlaces: 0,
});
interface IProps {
    /**
     * 文字
     */
    text?: string | number;
    /**
     * 文字颜色
     * @default #2D2D2D
     */
    textColor?: string;
    /**
     * 描边宽度
     * @default 1
     */
    strokeWidth?: string | number;
    /**
     * 描边颜色
     * @default #fff
     */
    strokeColor?: string;
    /**
     * 字体
     * @default PingFang SC
     */
    fontFamily?: string;
    /**
     * 字号
     * @default 24px
     */
    fontSize?: string;
    /**
     * 字重
     * @default 400
     */
    fontWeight?: string | number;
    /**
     * filter阴影配置:
     * 如果不开启阴影，不用传，或者传false;
     * 如果开启，需要传对象{x: 横向偏移, y: 纵向偏移, blur: 模糊值, color: 阴影颜色}
     * @default false
     */
    shadowFilterConfig?:
        | false
        | {
              x: number;
              y: number;
              blur: number;
              color: string;
          };
    /**
     * 是否需要数字动画
     * @default false
     */
    needAnimation?: boolean;
    reservedDecimalPlaces?: number;
}
const localText = ref(typeof props.text === 'number' && props.needAnimation ? 0 : props.text);

const changedNumber = ref<any>(localText.value);
function changeNumber(val: number) {
    changedNumber.value = val.toFixed(props.reservedDecimalPlaces || 0);
}
// if (props.needAnimation) {
watch(
    () => props.text,
    () => {
        // if (typeof props.text === 'number' && typeof localText.value === 'number') {
        //     // const totalDuration = 0.3
        //     // const delta = Math.abs(localText.value - props.text)
        //     // let timer = window.setInterval(async () => {
        //     //     await nextTick()
        //     //     if (Math.abs(localText.value as number) === Math.abs(props.text as number)) {
        //     //         window.clearInterval(timer)
        //     //         timer = -1
        //     //     }
        //     //     else {
        //     //         if (props.text > localText.value) {
        //     //             (localText.value as number)++
        //     //         }
        //     //         else if (props.text < localText.value) {
        //     //             (localText.value as number)--
        //     //         }
        //     //     }
        //     // }, (totalDuration * 1000) / delta)

        //     // localText.value = props.text
        // }
        // else {

        // }

        localText.value = props.text;
        changedNumber.value = props.text;
    },
    { immediate: true },
);
// }
</script>

<style lang="less" scoped>
.title-container {
    // 父元素为描边
    color: var(--stroke-color);
    font-family: var(--font-family);
    font-size: var(--font-size);
    font-weight: var(--font-weight);
    position: relative;
    filter: var(--shadow);
    -webkit-text-stroke: var(--stroke-width);

    &::before {
        // 伪元素为字本身
        content: attr(data-name);
        position: absolute;
        left: 0;
        top: 0;
        color: var(--color);
        -webkit-text-stroke: initial;
        white-space: nowrap;
    }
}
</style>
