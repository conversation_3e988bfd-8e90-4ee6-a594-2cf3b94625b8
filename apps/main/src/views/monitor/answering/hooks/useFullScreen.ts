import type { DialogReturn } from '@boss/design';
import { userCutScreenDialog } from '@/utils/system-notification-dialog';
import { beFull, exitFull, isFull } from '@crm/exam-utils/src/tools/fullscreen';
// import { requestFullscreenWithScreenShare, safeExitFullscreen, detectScreenShareType } from '@/utils/enhanced-fullscreen';
import { onUnmounted, ref } from 'vue';
import { switchScreenIncrease } from './useSwitchScreen';
import { useDebuggingStore } from '../../store';

// 组件卸载状态标记
let isComponentUnmounted = ref(false);
let fullscreenDialogRef: DialogReturn | null = null;
let isDialogShowing = ref(false);
let isProcessingFullscreenChange = ref(false);
let fullscreenCheckTimer: any = null;
// 存储所有待清理的定时器
let pendingTimeouts: Set<any> = new Set();

function closeDialogs() {
    // 检查组件是否已卸载
    if (isComponentUnmounted.value) {
        return;
    }

    if (fullscreenDialogRef?.close) {
        try {
            fullscreenDialogRef.close();
        } catch (error) {
            logger.error('关闭对话框失败:', error);
        }
    }
    fullscreenDialogRef = null;
    isDialogShowing.value = false;
}

// 清理定时器
function clearFullscreenTimer() {
    if (fullscreenCheckTimer) {
        clearTimeout(fullscreenCheckTimer);
        fullscreenCheckTimer = null;
    }
}

// 安全的 setTimeout，会在组件卸载时自动清理
function safeSetTimeout(callback: () => void, delay: number): any {
    if (isComponentUnmounted.value) {
        return null;
    }

    const timeoutId = setTimeout(() => {
        // 执行前再次检查组件是否已卸载
        if (!isComponentUnmounted.value) {
            pendingTimeouts.delete(timeoutId);
            callback();
        }
    }, delay);

    pendingTimeouts.add(timeoutId);
    return timeoutId;
}

// 清理所有待处理的定时器
function clearAllPendingTimeouts() {
    pendingTimeouts.forEach((timeoutId) => {
        clearTimeout(timeoutId);
    });
    pendingTimeouts.clear();
}

// 创建对话框的辅助函数，使用 force 参数确保可以创建
async function createFullscreenDialog(): Promise<DialogReturn | null> {
    // 检查组件是否已卸载
    if (isComponentUnmounted.value) {
        return null;
    }

    // 先确保完全清理
    closeDialogs();

    // 等待一帧确保清理完成
    await new Promise((resolve) => {
        const timeoutId = setTimeout(resolve, 50);
        pendingTimeouts.add(timeoutId);
    });

    // 再次检查组件是否已卸载
    if (isComponentUnmounted.value) {
        return null;
    }

    try {
        const dialog = userCutScreenDialog(
            {
                title: '全屏考试',
                content: '请您点击下方按钮开启全屏模式进行考试，并保持考试全程为全屏模式，如果退出全屏将视为作弊。',
                type: 'info',
                showCancel: false,
                showClose: false,
                confirmText: '开启全屏',
                layerClosable: false,
                enableEscClose: false,
                async confirm() {
                    // 检查组件是否已卸载
                    if (isComponentUnmounted.value) {
                        return;
                    }

                    isProcessingFullscreenChange.value = true;

                    try {
                        await beFull();

                        // 再次检查组件是否已卸载
                        if (isComponentUnmounted.value) {
                            return;
                        }

                        // 埋点 开启全屏
                        BossAnalyticsTrack('zhice-pc-exam-begin-fullscreen', {
                            pData: {
                                nameZh: '开启全屏',
                            },
                        });

                        // 关闭对话框
                        if (!isComponentUnmounted.value) {
                            isDialogShowing.value = false;
                            fullscreenDialogRef = null;
                        }
                    } catch (error) {
                        if (!isComponentUnmounted.value) {
                            isDialogShowing.value = false;

                            // 延迟重试
                            safeSetTimeout(() => {
                                executeAskFullFlow();
                            }, 1000);
                        }
                    } finally {
                        if (!isComponentUnmounted.value) {
                            isProcessingFullscreenChange.value = false;
                        }
                    }
                },
                onClose() {
                    if (!isComponentUnmounted.value) {
                        isDialogShowing.value = false;
                        fullscreenDialogRef = null;
                    }
                },
            },
            true,
        ); // force = true

        if (dialog) {
            return dialog;
        } else {
            logger.warn('userCutScreenDialog 返回值为:', dialog);
            return null;
        }
    } catch (error) {
        logger.error('userCutScreenDialog 创建对话框异常:', error);
        return null;
    }
}

const executeAskFullFlow = async () => {
    // 检查组件是否已卸载
    if (isComponentUnmounted.value) {
        return;
    }

    // 如果正在处理全屏变化或对话框已经显示，跳过
    if (isProcessingFullscreenChange.value || isDialogShowing.value) {
        return;
    }

    if (isFull()) {
        return;
    }

    isDialogShowing.value = true;

    try {
        fullscreenDialogRef = await createFullscreenDialog();

        // 再次检查组件是否已卸载
        if (isComponentUnmounted.value) {
            return;
        }

        if (!fullscreenDialogRef) {
            // 如果创建失败，重置状态并稍后重试
            isDialogShowing.value = false;
            safeSetTimeout(() => {
                executeAskFullFlow();
            }, 2000);
        }
    } catch (error) {
        logger.error('创建对话框失败:', error);

        if (!isComponentUnmounted.value) {
            isDialogShowing.value = false;
            fullscreenDialogRef = null;

            // 重试
            safeSetTimeout(() => {
                executeAskFullFlow();
            }, 2000);
        }
    }
};

function askFull() {
    executeAskFullFlow();
}

const executeExitHandlerFlow = async () => {
    // 检查组件是否已卸载
    if (isComponentUnmounted.value) {
        return;
    }

    const isCurrentlyFull = isFull();
    if (isProcessingFullscreenChange.value) {
        return;
    }

    if (!isCurrentlyFull) {
        isProcessingFullscreenChange.value = true;

        // 关闭可能存在的对话框
        closeDialogs();

        // 埋点
        BossAnalyticsTrack('zhice-pc-exam-paper-exit-fullscreen', {
            pData: {
                type: TrackTypeEnum.失败,
                nameZh: '退出全屏',
            },
        });

        try {
            const committed = await switchScreenIncrease();

            // 再次检查组件是否已卸载
            if (isComponentUnmounted.value) {
                return;
            }

            if (!committed) {
                // 重置状态并请求全屏
                isProcessingFullscreenChange.value = false;
                safeSetTimeout(() => {
                    askFull();
                }, 300);
            } else {
                isProcessingFullscreenChange.value = false;
            }
        } catch (error) {
            logger.error('switchScreenIncrease failed:', error);

            if (!isComponentUnmounted.value) {
                isProcessingFullscreenChange.value = false;

                // 出错时也要重新请求全屏
                safeSetTimeout(() => {
                    askFull();
                }, 1000);
            }
        }
    } else {
        closeDialogs();
    }
};

// 简化的退出处理函数
async function exitHandler() {
    // 检查组件是否已卸载
    if (isComponentUnmounted.value) {
        return;
    }

    // 简单的防抖处理
    clearFullscreenTimer();

    if (!isComponentUnmounted.value) {
        fullscreenCheckTimer = setTimeout(async () => {
            // 执行前再次检查组件是否已卸载
            if (!isComponentUnmounted.value) {
                await executeExitHandlerFlow();
            }
        }, 50);
    }
}

function bindFullScreenEvents() {
    // 绑定各种全屏事件
    document.addEventListener('webkitfullscreenchange', exitHandler, false);
    document.addEventListener('mozfullscreenchange', exitHandler, false);
    document.addEventListener('fullscreenchange', exitHandler, false);
    document.addEventListener('MSFullscreenChange', exitHandler, false);

    // 额外监听 keydown 事件，特别处理 ESC 键
    document.addEventListener('keydown', handleKeyDown, false);
}

function removeFullScreenEvents() {
    document.removeEventListener('webkitfullscreenchange', exitHandler, false);
    document.removeEventListener('mozfullscreenchange', exitHandler, false);
    document.removeEventListener('fullscreenchange', exitHandler, false);
    document.removeEventListener('MSFullscreenChange', exitHandler, false);
    document.removeEventListener('keydown', handleKeyDown, false);
    clearFullscreenTimer();
}

// 处理键盘事件，特别是 ESC 键
function handleKeyDown(event: KeyboardEvent) {
    // 检查组件是否已卸载
    if (isComponentUnmounted.value) {
        return;
    }

    if (event.key === 'Escape' || event.keyCode === 27) {
        // ESC 键被按下，延迟检查全屏状态
        safeSetTimeout(() => {
            executeExitHandlerFlow();
        }, 100);
    }
}

export default function () {
    const debuggingStore = useDebuggingStore();

    // 重置组件卸载状态
    isComponentUnmounted.value = false;

    if (debuggingStore.examConfig.switchScreenRule.fullScreen === 1) {
        // 初始检查全屏状态
        safeSetTimeout(() => {
            if (!isFull()) {
                askFull();
            }
        }, 500);
        bindFullScreenEvents();
    }

    onUnmounted(() => {
        // 标记组件已卸载
        isComponentUnmounted.value = true;

        // 清理所有事件监听器
        removeFullScreenEvents();

        // 清理所有定时器
        clearFullscreenTimer();
        clearAllPendingTimeouts();

        // 关闭对话框
        closeDialogs();

        // 退出全屏
        exitFull().catch((error) => {
            logger.warn('退出全屏失败:', error);
        });

        // 重置所有状态
        fullscreenDialogRef = null;
        isDialogShowing.value = false;
        isProcessingFullscreenChange.value = false;
    });
}
