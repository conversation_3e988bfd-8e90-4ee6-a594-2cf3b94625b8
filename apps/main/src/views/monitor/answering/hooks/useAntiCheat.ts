/* eslint-disable max-depth */
import NebulaMatrix from '@nebula/bodydetect-web';
import <PERSON><PERSON> from 'bowser';
import { dataURLtoFile } from '@crm/exam-utils';
import { logger } from '@/utils/logger';
import { post } from '@/services/http';
import { _substituteCheck } from '@/services/apis/exam';
import { nextTick, watch, onUnmounted } from 'vue';
import { useDebuggingStore } from '../../store';

const browser = Bowser.getParser(window.navigator.userAgent);
const browserObj = browser.getBrowser();

enum MonitoringTypeEnum {
    离开 = 1,
    低头 = 2,
    左右张望 = 3,
    多人脸 = 4,
    替考 = 5,
}

interface IParams {
    encryptExamId: string;
}

export default function ({ encryptExamId }: IParams) {
    logger.info('[useAntiCheat] init');
    const debuggingStore = useDebuggingStore();
    const nebulaMatrix = new NebulaMatrix();
    if (browserObj.name === 'Firefox') {
        nebulaMatrix.setBodyDetectProfile({ width: 640, height: 480 });
    } else {
        nebulaMatrix.setBodyDetectProfile({ width: 480, height: 360 });
    }

    let timer = -1;
    let totalTime = 0;
    let detectRule = <any>[];
    let isAntiCheatRunning = false;
    let retryTimer = -1;
    let initAttempts = 0;
    const MAX_INIT_ATTEMPTS = 5;

    const readConfig = () => {
        const examConfig = debuggingStore.examConfig;
        const {
            substituteExam,
            substituteExamLimitTimeWindow,
            substituteExamLimit,
            multipleFaces,
            multipleFacesLimitTimeWindow,
            multipleFacesLimit,
            leaveSeat,
            leaveSeatLimitTimeWindow,
            leaveSeatLimit,
            lowerHead,
            lowerHeadLimitTimeWindow,
            lowerHeadLimit,
            lookAround,
            lookAroundLimitTimeWindow,
            lookAroundLimit,
        } = examConfig.cameraMonitoringRule;

        if (substituteExam) {
            detectRule.push({ frequency: substituteExamLimitTimeWindow, limit: substituteExamLimit, code: [], mean: '替考' });
        }

        if (multipleFaces) {
            detectRule.push({ frequency: multipleFacesLimitTimeWindow, limit: multipleFacesLimit, code: [2003], mean: '多人脸' });
        }

        if (leaveSeat) {
            detectRule.push({ frequency: leaveSeatLimitTimeWindow, limit: leaveSeatLimit, code: [2002], mean: '离开' });
        }

        if (lowerHead) {
            detectRule.push({ frequency: lowerHeadLimitTimeWindow, limit: lowerHeadLimit, code: [10001], mean: '低头' });
        }

        if (lookAround) {
            detectRule.push({ frequency: lookAroundLimitTimeWindow, limit: lookAroundLimit, code: [10003, 10004], mean: '左右张望' });
        }
    };

    async function executeSubstituteCheckFlow(fd: FormData): Promise<any> {
        try {
            const res = await _substituteCheck(fd);
            if (res.code === 0) {
                // 替考检查成功
            } else {
                // '替考检查失败
            }
            return res;
        } catch (err: any) {
            // 替考检查请求异常
            // throw err;
            return null;
        }
    }

    const _cameraIncrease = (params: any) => {
        return post('/wapi/web/exam/cameraMonitoring/increase.json', params, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        });
    };

    async function executeCameraIncreaseFlow(fd: FormData): Promise<any> {
        try {
            const res = await _cameraIncrease(fd);
            return res;
        } catch (err: any) {
            // throw err;
            return null;
        }
    }

    function executeStartAntiCheatFlow() {
        if (isAntiCheatRunning) {
            // 防作弊已在运行中，跳过重复初始化
            return;
        }

        // 创建人体检测流
        nebulaMatrix.createBodyDetectStream({
            id: 'body_detect',
        });

        const detectMap: any = {};
        const dialogShowMap: any = {};
        for (let i = 0; i < detectRule.length; i++) {
            const ruleItem = detectRule[i];
            detectMap[ruleItem.mean] = [];
            dialogShowMap[ruleItem.mean] = false;
        }

        timer = window.setInterval(async () => {
            totalTime++;
            let detectResult = nebulaMatrix.getDetectResult();
            const timeHitRuleNames = detectRule.filter((ruleItem: any) => totalTime % ruleItem.frequency === 0).map((ruleItem: any) => ruleItem.mean);
            for (let i = 0; i < timeHitRuleNames.length; i++) {
                const hitRuleName = timeHitRuleNames[i];
                const ruleItem = detectRule.find((x: any) => x.mean === hitRuleName);

                if (hitRuleName === '替考') {
                    if (detectResult === 10000) {
                        const detectImage = nebulaMatrix.getCurrentFrameImage();
                        const fd = new FormData();
                        fd.append('file', dataURLtoFile(detectImage, 'file'));
                        fd.append('encryptExamId', encryptExamId as string);
                        fd.append('timestamp', String(Date.now()));

                        try {
                            const res: any = await executeSubstituteCheckFlow(fd);
                            if (res.code === 0) {
                                detectMap[hitRuleName].push({ detectCode: res.data.verified, timestamp: res.data.timestamp, detectImage });
                                detectMap[hitRuleName].sort((a: any, b: any) => a.timestamp - b.timestamp);
                                BossAnalyticsTrack('zhice-pc-exam-anti-cheat-substitute-request', {
                                    pData: {
                                        type: TrackTypeEnum.成功,
                                        message: res,
                                    },
                                });
                            } else {
                                BossAnalyticsTrack('zhice-pc-exam-anti-cheat-substitute-request', {
                                    pData: {
                                        type: TrackTypeEnum.失败,
                                        message: res,
                                        errorText: res,
                                    },
                                });
                            }

                            if (detectMap[hitRuleName].length >= ruleItem!.limit) {
                                BossAnalyticsTrack('zhice-pc-exam-cheat-detect-max', {
                                    pData: {
                                        type: TrackTypeEnum.成功,
                                        message: detectMap[hitRuleName].map((x: any) => x.detectCode),
                                    },
                                });
                                if (detectMap[hitRuleName].every((x: any) => x.detectCode === false)) {
                                    cameraIncrease(detectMap[hitRuleName], MonitoringTypeEnum.替考);
                                    detectMap[hitRuleName] = [];
                                } else {
                                    detectMap[hitRuleName].shift();
                                }
                            }
                        } catch (err: any) {
                            BossAnalyticsTrack('zhice-pc-exam-anti-cheat-substitute-request', {
                                pData: {
                                    type: TrackTypeEnum.失败,
                                    errorText: err.message || err.name,
                                },
                            });
                        }
                    }
                } else {
                    detectMap[hitRuleName].push({ detectCode: detectResult, detectImage: nebulaMatrix.getCurrentFrameImage() });
                    if (detectMap[hitRuleName].length >= ruleItem!.limit) {
                        BossAnalyticsTrack('zhice-pc-exam-cheat-detect-max', {
                            pData: {
                                type: TrackTypeEnum.成功,
                                message: detectMap[hitRuleName].map((x: any) => x.detectCode),
                            },
                        });
                        if (detectMap[hitRuleName].every((x: any) => ruleItem?.code.includes(x.detectCode))) {
                            // @ts-ignore
                            let content = '';
                            // @ts-ignore
                            let title = '';
                            let monitoringType: MonitoringTypeEnum | null = null;
                            switch (hitRuleName) {
                                case '离开':
                                    title = '检测到您离开摄像范围';
                                    content = '答题中请勿离开答题位置，保证全脸在电脑摄像头中，否则会被判定为作弊！';
                                    monitoringType = MonitoringTypeEnum.离开;
                                    break;
                                case '低头':
                                    title = '检测到您低头';
                                    content = '答题中请勿看向非屏幕方向，保证全脸在电脑摄像头中，否则会被判定为作弊！';
                                    monitoringType = MonitoringTypeEnum.低头;
                                    break;
                                case '左右张望':
                                    title = '检测到您左右张望';
                                    content = '答题中请勿看向非屏幕方向，保证全脸在电脑摄像头中，否则会被判定为作弊！';
                                    monitoringType = MonitoringTypeEnum.左右张望;
                                    break;
                                case '多人脸':
                                    title = '检测到摄像范围内存在多人';
                                    content = '答题中请保证答题环境，请勿出现多个人员，否则会被判定为作弊！';
                                    monitoringType = MonitoringTypeEnum.多人脸;
                                    break;
                                default:
                                    break;
                            }

                            if (monitoringType !== null) {
                                cameraIncrease(detectMap[hitRuleName], monitoringType);
                            }
                            detectMap[hitRuleName] = [];
                        } else {
                            detectMap[hitRuleName].shift();
                        }
                    }
                }
            }
        }, 1000);

        isAntiCheatRunning = true;
        initAttempts = 0;
    }

    const checkAntiCheatConditions = () => {
        const cameraStatus = debuggingStore.STATUS.camera.status;
        const nebulaLoaded = debuggingStore.antiCheatStatus.nebulaLoaded;
        const needAntiCheat = debuggingStore.antiCheatStatus.needAntiCheat;

        console.log(1111, needAntiCheat);

        return {
            cameraStatus,
            nebulaLoaded,
            needAntiCheat,
            allConditionsMet: cameraStatus === 2 && nebulaLoaded && needAntiCheat,
        };
    };

    const startAntiCheat = async () => {
        const conditions = checkAntiCheatConditions();

        if (!conditions.needAntiCheat) {
            return;
        }

        if (conditions.allConditionsMet) {
            if (!isAntiCheatRunning) {
                executeStartAntiCheatFlow();
                BossAnalyticsTrack('zhice-pc-exam-anti-cheat', {
                    pData: {
                        type: TrackTypeEnum.成功,
                        message: 'open',
                        nameZh: '开启反作弊',
                    },
                });
            }
        } else {
            if (initAttempts < MAX_INIT_ATTEMPTS) {
                initAttempts++;

                if (retryTimer !== -1) {
                    window.clearTimeout(retryTimer);
                }

                retryTimer = window.setTimeout(() => {
                    startAntiCheat();
                }, 1000 * initAttempts);
            } else {
                logger.error('[useAntiCheat] 达到最大重试次数，放弃启动防作弊');
                stopAntiCheat();
                BossAnalyticsTrack('zhice-pc-exam-anti-cheat', {
                    pData: {
                        type: TrackTypeEnum.失败,
                        message: 'max_retry_reached',
                        nameZh: '防作弊启动失败-达到最大重试次数',
                    },
                });
            }
        }
    };

    watch(
        () => [debuggingStore.STATUS.camera.status, debuggingStore.antiCheatStatus.nebulaLoaded, debuggingStore.antiCheatStatus.needAntiCheat],
        async () => {
            readConfig();

            if (retryTimer !== -1) {
                window.clearTimeout(retryTimer);
                retryTimer = -1;
            }

            await nextTick();

            setTimeout(() => {
                startAntiCheat();
            }, 100);
        },
        { immediate: true },
    );

    const stopAntiCheat = () => {
        window.clearInterval(timer);
        timer = -1;

        if (retryTimer !== -1) {
            window.clearTimeout(retryTimer);
            retryTimer = -1;
        }

        try {
            if (debuggingStore.antiCheatStatus.nebulaLoaded) {
                nebulaMatrix.destroy();
            }
        } catch (error) {
            logger.error('[useAntiCheat] Error destroying NebulaMatrix:', error);
        }

        isAntiCheatRunning = false;
        initAttempts = 0;

        BossAnalyticsTrack('zhice-pc-exam-anti-cheat', {
            pData: {
                type: TrackTypeEnum.失败,
                message: 'close',
                nameZh: '关闭反作弊',
            },
        });
    };

    const cameraIncrease = async (detectList: Array<any>, monitoringType: MonitoringTypeEnum) => {
        console.log('[useAntiCheat]', detectList, monitoringType);

        const fd = new FormData();
        for (let i = 0; i < detectList.length; i++) {
            const file = dataURLtoFile(detectList[i].detectImage, `file${i}`);
            fd.append('file', file);
        }
        fd.append('encryptExamId', encryptExamId as string);
        fd.append('monitoringType', String(monitoringType));
        try {
            const res = await executeCameraIncreaseFlow(fd);
            if (res?.code === 0) {
                BossAnalyticsTrack('zhice-pc-exam-cheat-action-report', {
                    pData: {
                        type: TrackTypeEnum.成功,
                    },
                });
            } else {
                logger.warn(`[useAntiCheat] cameraIncrease failed for ${MonitoringTypeEnum[monitoringType]}. Response:`, res);
                BossAnalyticsTrack('zhice-pc-exam-cheat-action-report', {
                    pData: {
                        type: TrackTypeEnum.失败,
                        errorText: res,
                    },
                });
            }
        } catch (error: any) {
            logger.error(`[useAntiCheat] Error in cameraIncrease for ${MonitoringTypeEnum[monitoringType]}:`, error);
            BossAnalyticsTrack('zhice-pc-exam-cheat-action-report', {
                pData: {
                    type: TrackTypeEnum.失败,
                    errorText: error.message || error.name,
                },
            });
        }
    };

    onUnmounted(() => {
        stopAntiCheat();
    });
}
