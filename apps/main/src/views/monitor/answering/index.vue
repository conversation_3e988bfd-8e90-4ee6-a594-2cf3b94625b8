<template>
    <PaperTime v-if="!isExam" />
    <RouterView />
    <DialogExamMediaMonitorHint v-model="dialogVisibleMonitorHintCamera" v-on="{ dialogClosed }" />
    <DialogExamPhoneMonitorHint v-model="dialogVisibleMonitorHintPhone" v-on="{ dialogClosed }" />
    <DialogExamScreenMonitorHint v-model="dialogVisibleMonitorHintScreen" v-on="{ dialogClosed }" />
    <NetworkDetect />
    <div id="body_detect" style="display: none" />
</template>

<script lang="ts" setup>
import DialogExamMediaMonitorHint from '@/components/dialog-exam-media-monitor-hint.vue';
import DialogExamPhoneMonitorHint from '@/components/dialog-exam-phone-monitor-hint.vue';
import DialogExamScreenMonitorHint from '@/components/dialog-exam-screen-monitor-hint.vue';
import NetworkDetect from '@/components/network-detect.vue';
import { useReLoginFlow } from './hooks/useReLoginFlow';
import { watch, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import PaperTime from './components/paper-time.vue';
import { commitPaper } from '../utils';
import useMonitor from './hooks/useMonitor';
import useFullScreen from './hooks/useFullScreen';
import useSwitchScreen from './hooks/useSwitchScreen';
import useAntiCheat from './hooks/useAntiCheat';
import useDialogHint from './hooks/useDialogHint';
import { useEndTimeStore } from '@/store/exam-time';
import { useMonitorStore } from '@/store/use-monitor-store';
import { componentsMap } from './config';
import { ReplayTypeEnum } from './type';
import { useRouteParamId } from '../hooks/useRouteParamId';

const endTimeStore = useEndTimeStore();
const monitorStore = useMonitorStore();
const $router = useRouter();
const { seqId, examId } = useRouteParamId();

const type = computed(() => monitorStore.examBaseInfo.examInfo?.examType || ReplayTypeEnum.Exam); // 考试类型
const isExam = computed(() => type.value === ReplayTypeEnum.Exam); // 是否是考试

// 使用新的组合式函数
const { dialogVisibleMonitorHintCamera, dialogVisibleMonitorHintPhone, dialogVisibleMonitorHintScreen, dialogClosed } = useDialogHint();

async function initProcess() {
    useMonitor(); // 考试监控初始化完成

    useFullScreen(); // 全屏初始化完成

    useSwitchScreen({
        examId,
        forceSubmitExamFn: () => {
            commitPaper({ type: 'switchScreenForce', seqId, examId });
        },
    }); // '切换屏幕初始化完成'

    useAntiCheat({ encryptExamId: examId }); // 防作弊初始化完成

    useReLoginFlow({
        examId,
        forceSubmit: () => {
            commitPaper({ type: 'reLogin', seqId, examId });
        },
    }); // 重复登录弹窗流程初始化完成
}

// 非量表类测评交卷倒计时交卷，量表类测评交卷倒计时在内部实现(因量表类产品超时需要提交当前试题内容)
if (type.value !== ReplayTypeEnum.Evaluation) {
    watch(
        () => [endTimeStore.examEndTime.remainingTime.total, endTimeStore.examEndTimeStatus],
        ([time, status]) => {
            if (time !== undefined && time <= 0 && status === 1) {
                commitPaper({ type: 'timeout', seqId, examId });
            }
        },
    );
}

initProcess();

$router.push(`${componentsMap[type.value as ReplayTypeEnum]}?seqId=${seqId}&examId=${examId}`);

onUnmounted(() => {
    endTimeStore.clearExamEndTime();
});
</script>
