<template>
    <div class="setting-inner">
        <div class="setting-inner-title">第二视角监控设置</div>
        <div class="setting-inner-desc">本场考试将启用手机第二视角监控，请扫描二维码，开启后按下述要求架设好手机，未按要求开启可能影响您的考试成绩</div>
        <div class="setting-inner-mini">
            <div class="phone-panel">
                <div class="guid-box">
                    <div class="title">
                        <SvgIcon name="icon-question-circle-fill" width="16" height="16" />
                        <h3>第二视角设置指南</h3>
                    </div>
                    <div class="guid-content">
                        <div class="desc">
                            <dl>
                                <dt>环境要点：</dt>
                                <dd>1.完整的电脑屏幕、键盘</dd>
                                <dd>2.桌面整洁、无杂物</dd>
                                <dd>3.监控画面内不能出现其他人员</dd>
                            </dl>
                            <dl>
                                <dt>手机摆放：</dt>
                                <dd>1.手机摆放在正侧方</dd>
                                <dd>2.距离电脑 1-1.5 米处</dd>
                                <dd>3.架高手机，俯拍桌面</dd>
                            </dl>
                            <dl>
                                <dt>考生注意：</dt>
                                <dd>1.务必在监控画面中露出侧脸、上半身、双手</dd>
                                <dd>2.考试全程考生均在监控画面内</dd>
                            </dl>
                        </div>
                        <div class="example">
                            <b-image width="130" :src="demoImgUrl" />
                            <p class="gallery-name">手机示例图</p>
                        </div>
                    </div>
                </div>
                <div class="phone-box">
                    <div v-if="monitorStatus === 0" class="default phone-box-inner">
                        <div class="qr-code-box">
                            <H5ExamMonitorQrCode />
                        </div>
                        <p class="tip">手机扫码开启监控</p>
                    </div>
                    <div v-else-if="monitorStatus === 1" class="error phone-box-inner">
                        <div class="error-title">
                            <SvgIcon name="svg-full-error-hollow" width="16" height="16" />
                            <h3>画面异常</h3>
                        </div>
                        <div class="error-reason">第二视角异常，请重新扫码</div>
                        <H5ExamMonitorQrCode />
                        <div style="text-align: center; margin-top: 10px">
                            <p style="font-size: 12px; color: #888; margin-bottom: 8px">
                                扫描后请: <br />1. 确认手机已授予摄像头权限 <br />2. 确保手机网络通畅 <br />3. 不要退出或最小化手机页面
                            </p>
                        </div>
                    </div>
                    <div v-else-if="monitorStatus === 2" class="phone-box-inner success">
                        <div id="phoneRemoteStream-0" ref="phoneRemoteStreamRef" :style="{ width: '100%', height: '100%' }" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { usePrepareCheckSave } from '@/hooks/usePrepareCheckSave';

import { computed, ref, watch } from 'vue';
import demoImgUrl from './images/secondary.png';
import H5ExamMonitorQrCode from '@/components/h5-exam-monitor-qr-code.vue';
import { useDebuggingStore } from '../../../store';
import { useMonitorStore } from '@/store/use-monitor-store';
import { useRouteParamId } from '../../../hooks/useRouteParamId';

const props = defineProps({
    stepStatus: {
        type: String,
        default: 'error',
    },
    examId: {
        type: String,
        default: '',
    },
    seqId: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['changeStepClickable', 'update:stepStatus']);
const monitorStore = useMonitorStore();
const debugStore = useDebuggingStore();
const useRouteParam = useRouteParamId();

BossAnalyticsTrack('zhice-pc-exam-enter-phone-setting', {
    pData: {
        nameZh: '进入第二视角调试',
    },
});

// 考前准备第二视角状态更新
debugStore.updatePhoneCamera({
    encryptExamId: props.examId || props.seqId,
    status: 1,
});

// 打开第二视角
debugStore.openPhoneMonitor();

const phoneRemoteStreamRef = ref();
const monitorStatus = computed(() => debugStore.STATUS.phone.status); // 0 未开启、1 失败、2 成功
const phoneRemoteStream = computed(() => debugStore.STATUS.phone.remoteStream);

watchEffect(() => {
    if (phoneRemoteStream.value && monitorStatus.value === 2 && phoneRemoteStreamRef.value) {
        try {
            phoneRemoteStream.value.play('phoneRemoteStream-0', { muted: true }).catch((error: any) => {
                logger.error('第二视角视频流播放失败', error);
            });
        } catch (error) {
            logger.error('调用play方法出错', error);
        }
    }
});

watch(
    () => monitorStatus.value,
    (val: number) => {
        usePrepareCheckSave({
            encryptExamId: (useRouteParam.examId || useRouteParam.seqId) as string,
            checkItem: 6, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角
            checkStatus: val === 2 ? 1 : 2, // //状态 1成功 2失败
            checkRemark: val === 2 ? '' : '第二视角异常',
        });
        // 修改step × or ✓
        emit('update:stepStatus', val === 2 ? 'finish' : 'danger');
    },
);

if (monitorStatus.value === 0) {
    emit('update:stepStatus', 'danger');
}

const mode = computed(() => monitorStore.ExamMode); // PREPARE_MODE : EXAM_MODE
const nextClickable = computed(() => {
    if (mode.value === 'EXAM_MODE') {
        // 正式考试 第二视角不拦截
        // return monitorStatus.value === 2;
        return true;
    }
    return true;
});

watch(
    () => nextClickable.value,
    () => {
        emit('changeStepClickable', nextClickable.value);
    },
    { immediate: true },
);

function getNextCheckResult(cb: (str?: string) => void): boolean {
    if (!nextClickable.value) {
        return false;
    }
    if (mode.value === 'EXAM_MODE') {
        return true;
    }
    if (mode.value === 'PREPARE_MODE') {
        if (monitorStatus.value !== 2) {
            cb('您尚未成功开启第二视角，正式考试时第二视角缺失可能影响您的考试结果，确定要先进入下一步吗？');
            return false;
        }
        return true;
    }
    return false;
}

defineExpose({ getNextCheckResult });
</script>

<style lang="less">
@import './index.less';
</style>
