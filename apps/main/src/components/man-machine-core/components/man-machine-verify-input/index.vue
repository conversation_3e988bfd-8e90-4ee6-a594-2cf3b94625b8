<!-- 暂时用ui-input是为了减少替换的成本 -> 后续需要升级boss-design -->
<template>
    <ui-input v-bind="$attrs" :placeholder="placeholder" class="input-verify-code" :maxlength="maxlength">
        <template #suffix>
            <ManMachineCore
                ref="manMachineVerifyRef"
                v-bind="{
                    ...(ajaxFn && { ajaxFn }),
                    ...(beforeSend && { beforeSend }),
                    ...(extraReqParams && { extraReqParams }),
                    ...(successToast && { successToast }),
                }"
                @success="onSuccess"
                @fail="onFail"
            />
        </template>
    </ui-input>
</template>

<script setup lang="ts">
import type { IVerifyProps, IVerifyParams, IVerifyFailParams, ICountdownBtnProps } from '../../types';

import ManMachineCore from '../../index.vue';

interface IProps {
    maxlength?: number;
    placeholder?: string;
}

const emit = defineEmits<{
    (_e: 'success', _successParams: IVerifyParams): void; // 发送成功
    (_e: 'fail', _failParams: IVerifyFailParams): void; // 发送失败
}>();

withDefaults(defineProps<IProps & IVerifyProps & ICountdownBtnProps>(), {
    maxlength: 6,
    placeholder: '请输入',
    extraReqParams: () => ({}),
    mode: 'trigger',
    inline: false,
    beforeSend: () => Promise.resolve(true),
    btnText: '发送验证码',
    showCounter: true,
    successToast: '发送成功',
});

function onSuccess(verifyParams: IVerifyParams) {
    emit('success', verifyParams);
}

function onFail(failParams: IVerifyFailParams) {
    emit('fail', failParams);
}

defineOptions({ name: 'ManMachineVerifyInput' });
</script>

<style lang="less">
.input-verify-code {
    &.input-wrap {
        // 验证码
        &.input-verify-code {
            input.input {
                padding-right: 100px;
            }
            .suffix {
                right: 8px;
                &::before {
                    content: '';
                    display: block;
                    position: relative;
                    width: 1px;
                    height: 12px;
                    background: #d3d8e6;
                    right: 8px;
                }
            }
        }
    }
}
</style>
