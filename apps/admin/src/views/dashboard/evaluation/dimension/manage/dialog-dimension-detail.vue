<template>
    <b-dialog
        v-model="visible"
        :title="computedTitle"
        :layerClosable="false"
        :enableEscClose="false"
        wrapClass="dialog-add-evaluation-dimension"
        :beforeConfirm="handleBeforeConfirm"
        width="730px"
        @cancel="handleCancel"
    >
        <b-form ref="formRef" v-loading="showLoading" :model="formData" autoLabelWidth gentleValidation layout="vertical">
            <b-row :gutter="[20, 24]">
                <b-col :span="24">
                    <b-form-item class="combined-form-item" label="维度名称" asteriskPosition="end" required>
                        <b-select v-model="formData.productId" :options="productList" placeholder="请选择测评产品" :disabled="isEdit" @change="handleChangeProduct" />
                        <!-- 校验：不能与同测评产品下的其他所有状态的维度重名； -->
                        <b-input v-model.trim="formData.dimensionName" trimBeforePaste placeholder="请填写维度名称" :maxLength="10" showWordLimit allowClear />
                    </b-form-item>
                </b-col>
                <!-- 心理健康筛查测评 -->
                <MentalHealth v-if="formData.productId === PCode.HMA" ref="childComponentRef" v-model="formData" />
                <!-- 五维性格测评 -->
                <Character
                    v-else-if="[PCode.OEA, PCode.OEWA].includes(formData.productId)"
                    ref="childComponentRef"
                    v-model="formData"
                    :isEdit="isEdit"
                    :productId="formData.productId"
                    @changeProduct="handleChangeProduct"
                />
                <!-- 五维性格2.0测评 -->
                <Oema2
                    v-else-if="formData.productId === PCode.OEWA2"
                    ref="childComponentRef"
                    v-model:formData="formData"
                    :isEdit="isEdit"
                    :productId="formData.productId"
                    @changeProduct="handleChangeProduct"
                />
                <!-- 职业韧性测评 -->
                <ProfessionalWillfulness
                    v-else-if="professionalGroup"
                    ref="childComponentRef"
                    :key="formData.productId"
                    v-model="formData"
                    :productId="formData.productId"
                    :isEdit="isEdit"
                    @changeProduct="handleChangeProduct"
                />
                <!-- 领导力情景测评&心理健康测评 -->
                <LeadershipSkills
                    v-else-if="[PCode.OMHA, PCode.LPA].includes(formData.productId)"
                    ref="childComponentRef"
                    v-model="formData"
                    :productId="formData.productId"
                    :isEdit="isEdit"
                    @changeProduct="handleChangeProduct"
                />
                <!-- 16T职业性格测评 -->
                <TProfessional v-else-if="formData.productId === PCode.PTA" ref="childComponentRef" v-model="formData" :isEdit="isEdit" />
                <!-- 工作风格测评 -->
                <Disc v-else-if="formData.productId === PCode.DISC" ref="childComponentRef" v-model="formData" :productId="formData.productId" :isEdit="isEdit" />
                <!-- 高潜人测识别测评 -->
                <Hipo v-else-if="formData.productId === PCode.HIPO" ref="childComponentRef" v-model="formData" :productId="formData.productId" :isEdit="isEdit" />
                <!-- 认知能力测评 -->
                <Caa v-else-if="formData.productId === PCode.CAA" ref="childComponentRef" v-model="formData" :isEdit="isEdit" />
                <!-- 敬业调研度测评 -->
                <DedicationResearch v-else-if="formData.productId === PCode.ES" ref="childComponentRef" v-model="formData" :productId="formData.productId" :isEdit="isEdit" />
                <!-- 职业动机测评 -->
                <MotivationAssessment v-else-if="formData.productId === PCode.MA" ref="childComponentRef" v-model="formData" :productId="formData.productId" :isEdit="isEdit" />
                <!-- 政治素养测评 -->
                <PoliticalQuality v-else-if="formData.productId === PCode.PQA" ref="childComponentRef" v-model="formData" :productId="formData.productId" :isEdit="isEdit" />

                <!-- 其他 -->
                <BaseInfo v-else ref="childComponentRef" v-model="formData" />

                <b-col v-if="showPriority" :span="24">
                    <b-form-item label="展示优先级" field="priority" class="priority">
                        <b-input-number v-model="formData.priority" placeholder="请填写0~9999的整数，数字越小优先级越高" hideButton :min="0" :max="9999" :precision="0" />
                    </b-form-item>
                </b-col>
            </b-row>
        </b-form>
    </b-dialog>
</template>

<script setup lang="ts">
import { _getDimensionInfo, _saveDimension } from '@/services/api/dimension';
import { _getProductOptions } from '@/services/api/project';
import { PCode } from '@crm/biz-exam-product';
import { computed, ref } from 'vue';
import TProfessional from './components/dialog/16t-professional.vue';
import BaseInfo from './components/dialog/base-info.vue';
import Caa from './components/dialog/caa.vue';
import Character from './components/dialog/character.vue';
import Oema2 from './components/dialog/oema2.vue';
import DedicationResearch from './components/dialog/dedication-research.vue';
import Disc from './components/dialog/disc.vue';
import Hipo from './components/dialog/hipo.vue';
import LeadershipSkills from './components/dialog/leadership-skills.vue';
import MentalHealth from './components/dialog/mental-health.vue';
import ProfessionalWillfulness from './components/dialog/professional-willfulness.vue';
import MotivationAssessment from './components/dialog/motivation-assessment.vue';
import PoliticalQuality from './components/dialog/political-quality.vue';

defineOptions({
    name: 'DialogDimensionDetail',
});

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    encryptId: {
        type: String,
    },
});
const emits = defineEmits(['update:modelValue', 'success']);

const showLoading = ref(false);
const isEdit = computed(() => !!props.encryptId);
const computedTitle = computed(() => (isEdit.value ? '编辑维度' : '新增维度'));
// 是否展示优先级
const showPriority = computed(() => ![PCode.CAA, PCode.PTA, PCode.DISC, PCode.HIPO, PCode.ES, PCode.MA].includes(formData.value.productId));
// 职业韧性测评产品和金融人才测评产品公用一套逻辑
const professionalGroup = computed(() => {
    return [PCode.CRA, PCode.FCA].includes(formData.value.productId);
});
const childComponentRef = ref();
const initFormData = {
    productId: '',
    encryptParentId: undefined,
    dimensionName: '',
    normalAverageScore: undefined,
    normalDeviation: undefined,
    adviseAnswerTime: undefined,
    minValidAnswerTime: undefined,
    maxValidAnswerTime: undefined,
    crowd: [],
    description: '',
    priority: undefined,
    factorType: undefined,
    dimensionTendency: undefined,
    dimensionTendencyConfig: [],
    descriptionOf16T: {
        1: '',
        2: '',
    },
};
const formData = ref<any>(initFormData);
const formRef = ref();

function handleChangeProduct() {
    childComponentRef.value.resetFormData();
    if ([PCode.CRA, PCode.FCA, PCode.OMHA, PCode.LPA, PCode.DISC, PCode.ES, PCode.CAA, PCode.PTA, PCode.MA, PCode.PQA].includes(formData.value.productId)) {
        formData.value.description = undefined;
    }
    if ([PCode.PTA, PCode.DISC, PCode.ES, PCode.CAA, PCode.MA].includes(formData.value.productId)) {
        formData.value.priority = undefined;
    }
}

const productList = ref([]);
async function getDetail() {
    const params = {
        encryptId: props.encryptId,
    };
    try {
        showLoading.value = true;
        const res = await _getDimensionInfo(params);

        if (res.code === 0) {
            for (const key in initFormData) {
                if (Object.prototype.hasOwnProperty.call(formData.value, key)) {
                    let value = null;
                    if (key === 'crowd') {
                        value = res.data.crowd?.split(',') ?? [];
                    } else if (key === 'descriptionOf16T') {
                        value = formData.value[key] || { 1: '', 2: '' };
                    } else {
                        value = res.data[key];
                    }

                    formData.value[key] = value;
                }
            }
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    } finally {
        showLoading.value = false;
    }
}

async function getProductList() {
    const params = { source: 3 } as const;
    const res = await _getProductOptions(params);
    if (res.code === 0) {
        productList.value = res.data
            ?.map((x: any) => ({
                ...x,
                value: x.id,
                label: x.name,
            }))
            ?.filter((item: { value: number }) => ![PCode.HOTS, PCode.GBA, PCode.GPA].includes(item.value));
    }
}
async function getList() {
    await Promise.all([getProductList()]);
}

async function init() {
    await getList();
    if (isEdit.value) {
        getDetail();
    }
}

init();
function formValidate() {
    if (!(formData.value.productId && Number(formData.value.productId) >= 0)) {
        return { productId: { type: 'number', required: true, message: '请选择测评产品' } };
    }
    if (!formData.value.dimensionName) {
        return { dimensionName: { type: 'string', required: true, message: '请填写维度名称' } };
    }
    if (formData.value.dimensionName.length > 10) {
        return { dimensionName: { type: 'string', required: true, message: '维度名称最多支持输入10个字符' } };
    }
    return {};
}
const visible = ref(true);
async function handleBeforeConfirm() {
    const res = await formRef.value?.validate();
    const validFields = Object.assign(formValidate(), res || {});
    const childError = validFields[Object.keys(validFields)[0]];
    if (childError) {
        Toast.danger(childError.message);
        return false;
    }
    if (Number(formData.value.productId) === PCode.PTA) {
        const requiredName = formData.value.dimensionTendencyConfig?.every((item: { tendencyName: any }) => item.tendencyName);
        const requiredDesc = formData.value.dimensionTendencyConfig?.every((item: { tendencyDesc: any }) => item.tendencyDesc);
        const error = !requiredName ? '请输入倾向名称' : !requiredDesc ? '请输入描述' : undefined;
        if (error) {
            Toast.danger(error);
            return false;
        }
    }
    try {
        const params: any = {
            ...formData.value,
            crowd: formData.value?.crowd?.length ? formData.value?.crowd?.join(',') : undefined,
            dimensionTendencyConfig: formData.value?.dimensionTendencyConfig?.length ? formData.value?.dimensionTendencyConfig : undefined,
        };
        if (isEdit.value) {
            params.encryptId = props.encryptId;
        }
        const res: any = await _saveDimension(params);
        if (res.code === 0) {
            emits('success', isEdit.value);
            onClose();
            return true;
        } else {
            return false;
        }
    } catch (error: any) {
        return false;
    }
}
function handleCancel() {
    onClose();
}
function onClose() {
    emits('update:modelValue', false);
}
</script>

<style lang="less" scoped>
.priority {
    :deep(.b-form-item-wrapper-col) {
        width: 50%;
    }
}
.answerT-time-group {
    display: flex;
}
:deep(.min-valid-answer-time) {
    padding-right: 0 !important;
}
:deep(.max-valid-answer-time) {
    padding-left: 0 !important;
    .b-form-item-label-col {
        visibility: hidden;
    }
}
</style>

<style lang="less">
.dialog-add-evaluation-dimension {
    .combined-form-item {
        .b-select {
            width: 185px;
            border-right: none;
            border-radius: 4px 0 0 4px;
            flex-grow: 0;
            flex-shrink: 0;
        }
        .b-input-wrapper {
            border-radius: 0 4px 4px 0;
        }
    }
    .range-separator {
        flex-shrink: 0;
        margin: 0 8px;
        width: 12px;
        height: 1px;
        background: var(--gray-color-3);
    }
    .hint {
        margin-top: 6px;
        font-size: 12px;
        color: #808080;
        display: flex;
        align-items: center;
        svg {
            color: #ccc;
        }
        .hint-text {
            margin-left: 4px;
        }
    }
    .input-number-block-suffix {
        padding-right: 0;
        overflow: hidden;
        .b-input-suffix {
            padding-right: 8px;
            background: #f2f2f2;
            align-self: stretch;
            color: #1f1f1f;
        }
    }
}
</style>
