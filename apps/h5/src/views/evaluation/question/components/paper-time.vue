<template>
    <template v-if="show">
        <div class="phone-inner">
            <SvgIcon class="warn-icon" icon="red-warn" size="16px" />
            <span class="text">
                距离本次测试结束还有 {{ countdownTime?.minutes[0] }}{{ countdownTime?.minutes[1] }}:{{ countdownTime?.seconds[0] }}{{ countdownTime?.seconds[1] }} ，请合理分配时间
            </span>
        </div>
    </template>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useEndTimeStore } from '@/stores/exam-time';

const endTimeStore = useEndTimeStore();

// 场次结束时间
const countdownTime = computed(() => endTimeStore.examEndTime?.remainingTime);

const emit = defineEmits(['paperTimeEnd']);
const show = ref(false);
const tipsTime = 10 * 60 * 1000;

watch(
    () => [endTimeStore.examEndTime.remainingTime.total, endTimeStore.examEndTimeStatus],
    ([time, status]) => {
        if (time !== undefined && time <= tipsTime && status === 1) {
            show.value = true;
        }
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.phone-inner {
    padding: 8px 0;
    width: 100%;
    height: 34px;
    background: rgba(255, 120, 71, 0.16);
    display: flex;
    justify-content: center;

    .warn-icon {
        transform: translateY(1px);
        margin-right: 8px;
        width: 16px;
        height: 16px;
    }

    .text {
        color: #ff7847;
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
    }
}
</style>
