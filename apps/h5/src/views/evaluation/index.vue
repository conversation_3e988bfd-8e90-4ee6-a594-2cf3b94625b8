<template>
    <!-- <p>距离场次结束时间: {{ examStore.examCountdown.remainingTime.total }}</p> -->
    <RouterView />
</template>

<script lang="ts" setup>
import { useExamStore } from '@/stores/exam';
import { computed } from 'vue';
import { RouterView } from 'vue-router';
import { jumpExamList } from '@/utils/jump-url';

defineOptions({
    name: 'Evaluation',
    beforeRouteEnter: async (to, _from, next) => {
        const examStore = useExamStore();
        const examInfo = computed(() => examStore.examBaseInfo.examInfo);

        // 如果试卷名称存在 则直接放行
        if (examInfo.value.examName) {
            next();
            return;
        }

        const { seqId, examId } = to.query || {};

        if (!seqId || !examId) {
            jumpExamList();
            return;
        }

        await examStore.getSessionInfo({
            seqId: seqId as string,
            examId: examId as string,
        });

        let path = '';

        // 如果试卷已提交，则返回试卷列表页
        if (examInfo.value.hasCommitPaper) {
            jumpExamList();
            return;
        }

        // 需要展示指导语
        if (examInfo.value.paperReadyClickTimeTs <= 0) {
            path = '/evaluation/guidance';
        } else {
            path = '/evaluation/question';
        }

        next({
            path,
            query: {
                seqId,
                examId,
            },
        });
    },
});
</script>
