// 对象序列化
export function formatParams(obj: { [x: string]: any }, needBlank = false) {
    let str = '';

    const entries = Object.entries(obj);
    entries.forEach((item) => {
        if (needBlank || typeof item[1] === 'number' || item[1]) {
            str += str.length ? `&${item[0]}=${item[1]}` : `${item[0]}=${item[1]}`;
        }
    });

    return str;
}

/**
 * parseURL
 * @param {string} window.location.href || url
 */

export function parseURL(url: string) {
    const a = document.createElement('a');
    a.href = url;
    return {
        origin: a.origin,
        pathname: a.pathname,
        source: url,
        protocol: a.protocol.replace(':', ''),
        host: a.hostname,
        port: a.port,
        query: a.search,
        params: (function () {
            let params = {};
            const seg = a.search.replace(/^\?/, '').split('&');
            const len = seg.length;
            let p;
            for (let i = 0; i < len; i++) {
                if (seg[i]) {
                    p = seg[i]!.split('=');
                    params = {
                        [p[0] as any]: p[1],
                        ...params,
                    };
                }
            }
            return params;
        })(),
        hash: a.hash.replace('#', ''),

        path: a.pathname.replace(/^([^/])/, '/$1'),
    };
}

export const visibilityChange = 'visibilityState' in document ? 'visibilitychange' : 'webkitVisibilityState' in document ? 'webkitVisibilityChange' : null;
export function getVisibilityState() {
    return document.visibilityState || (document as any).webkitVisibilityState || 'visible';
}

/**
 * 判断是否为外部地址
 * @param path 文件路径
 * @returns boolean
 */
export function isExternal(path: string) {
    return /^(?:https?:|mailto:|tel:)/.test(path);
}
