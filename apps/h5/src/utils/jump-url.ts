import { DEPLOY_ENV } from '@/constant/app';
import { parseURL } from './index';

const isDevelopment = DEPLOY_ENV === 'dev';

/**
 * 跳转到登录页面
 * 根据当前环境构建登录URL并执行页面跳转
 * 开发环境下会跳转到8080端口
 * 跳转前会先执行登出操作
 */
export async function jumpLogin() {
    const { hostname, search } = window.location;
    const { seqId } = parseURL(search).params as any;
    const { loginType = 1 } = parseURL(search).query as any;

    Invoke.login.postLogout();

    let url = `/login?seqId=${seqId}&loginType=${loginType}`;

    if (isDevelopment) {
        url = `//${hostname}:8080${url}`;
    }

    window.location.replace(url);
}

/**
 * 跳转到考试列表页面
 * 根据当前环境构建跳转URL并执行页面跳转
 * 开发环境下会跳转到8080端口
 */
export function jumpExamList() {
    const { hostname, search } = window.location;
    const { seqId } = parseURL(search).params as any;
    let url = `/exam-list/${seqId}`;

    if (isDevelopment) {
        url = `//${hostname}:8080${url}`;
    }

    window.location.replace(url);
}
