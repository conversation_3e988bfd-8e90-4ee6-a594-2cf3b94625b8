# Git Hooks 门禁配置

本项目配置了完整的 Git 钩子门禁系统，确保代码质量和项目稳定性。

## 概述

项目使用 [Husky](https://typicode.github.io/husky/) 来管理 Git 钩子，配置了以下检查：

- **pre-commit**: 提交前检查
- **pre-push**: 推送前检查
- **commit-msg**: 提交信息格式检查

## Pre-commit 检查

在每次 `git commit` 时自动执行以下检查：

### 1. 代码格式化和 ESLint 检查
- 使用 `lint-staged` 对暂存区文件进行格式化
- 运行 ESLint 检查并自动修复可修复的问题
- 使用 Prettier 格式化代码

### 2. TypeScript 类型检查
- 运行 `vue-tsc --noEmit` 进行类型检查
- 确保没有类型错误

### 3. Console.log 检查（警告）
- 检查暂存区文件中的 `console.log` 语句
- 仅显示警告，不阻止提交

### 4. 分支保护
- 防止直接提交到 `main` 或 `master` 分支
- 强制使用功能分支进行开发

### 5. 大文件检查
- 检查文件大小，防止提交超过 500KB 的大文件

## Pre-push 检查

在每次 `git push` 时自动执行以下检查：

### 1. 远程分支同步
- 获取最新的远程分支信息

### 2. TypeScript 构建检查
- 运行 `vue-tsc --build` 进行完整构建
- 确保项目可以正常构建

### 3. TypeScript 类型检查
- 再次运行类型检查确保没有类型错误

### 4. 测试执行
- 运行 `pnpm run test:ci` 执行所有测试
- 确保所有测试用例通过

### 5. ESLint 检查
- 运行完整的 ESLint 检查
- 确保代码符合规范

## Commit 消息格式检查

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

### 支持的类型

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式（不影响功能）
- `refactor`: 重构
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建相关
- `ci`: CI/CD 相关
- `chore`: 其他杂项
- `revert`: 回滚
- `wip`: 开发中
- `workflow`: 工作流改进
- `types`: 类型定义
- `release`: 发布
- `deps`: 依赖更新
- `config`: 配置更新
- `merge`: 合并分支
- `security`: 安全更新
- `breaking`: 重大变更
- `upgrade`: 升级

### 示例

```bash
feat(auth): 添加用户登录功能
fix(api): 修复用户信息获取接口错误
docs: 更新 API 文档
style: 格式化代码
refactor(utils): 重构工具函数
```

## 手动执行检查

### 测试 pre-commit 检查
```bash
pnpm run hooks:pre-commit
```

### 测试 pre-push 检查
```bash
pnpm run hooks:pre-push
```

### 测试所有检查
```bash
pnpm run hooks:test
```

## 跳过检查（不推荐）

在紧急情况下，可以跳过检查：

```bash
# 跳过 pre-commit 检查
git commit --no-verify -m "emergency fix"

# 跳过 pre-push 检查
git push --no-verify
```

**注意**: 跳过检查可能导致代码质量问题，仅在紧急情况下使用。

## 故障排除

### 1. TypeScript 构建失败
```bash
# 查看详细错误
pnpm run tsc:build

# 清理构建缓存
pnpm run tsc:clean
pnpm run tsc:build
```

### 2. 测试失败
```bash
# 运行测试查看详细信息
pnpm run test:ci

# 运行监视模式进行调试
pnpm run test
```

### 3. ESLint 错误
```bash
# 自动修复可修复的问题
pnpm run lint

# 查看所有错误
npx eslint . --cache
```

### 4. 重新安装 Husky
```bash
# 重新安装 Git 钩子
pnpm run prepare
```

## 配置文件

- `.husky/pre-commit`: pre-commit 钩子脚本
- `.husky/pre-push`: pre-push 钩子脚本
- `.husky/commit-msg`: commit 消息检查脚本
- `commitlint.config.js`: commit 消息规范配置
- `scripts/git-hooks-check.js`: 检查脚本
- `package.json`: lint-staged 配置

## 最佳实践

1. **频繁提交**: 保持小而频繁的提交
2. **功能分支**: 始终在功能分支上开发
3. **测试先行**: 编写测试确保代码质量
4. **类型安全**: 充分利用 TypeScript 的类型检查
5. **代码规范**: 遵循项目的代码规范和格式化规则
